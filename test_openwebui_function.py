#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for Open WebUI Document Embedding Search Function

This script tests the Open WebUI function to ensure it works correctly
before deploying to the Docker container.
"""

import asyncio
import json
import sys
from pathlib import Path

# Import the Open WebUI function
try:
    from openwebui_embedding_search import pipe, get_search_system
    print("✅ Successfully imported Open WebUI function")
except ImportError as e:
    print(f"❌ Failed to import Open WebUI function: {e}")
    sys.exit(1)

async def test_openwebui_function():
    """Test the Open WebUI function with sample requests"""
    
    print("Testing Open WebUI Document Embedding Search Function")
    print("=" * 60)
    
    # Test cases
    test_cases = [
        {
            "name": "High Confidence Query",
            "messages": [
                {"role": "user", "content": "What is Sentry?"}
            ]
        },
        {
            "name": "Medium Confidence Query", 
            "messages": [
                {"role": "user", "content": "How does ClickHouse work?"}
            ]
        },
        {
            "name": "Low Confidence Query",
            "messages": [
                {"role": "user", "content": "What is quantum physics?"}
            ]
        },
        {
            "name": "Unrelated Query (Should trigger threshold)",
            "messages": [
                {"role": "user", "content": "How to cook pasta?"}
            ]
        }
    ]
    
    # Test each case
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test {i}: {test_case['name']} ---")
        
        # Create request body
        body = {
            "messages": test_case["messages"],
            "model": "test-model"
        }
        
        try:
            # Call the pipe function
            result = await pipe(body)
            
            # Parse and display result
            if isinstance(result, str):
                # If result is JSON string, parse it
                try:
                    parsed_result = json.loads(result)
                    print("Response:")
                    print(json.dumps(parsed_result, ensure_ascii=False, indent=2))
                    
                    # Analyze result
                    if parsed_result.get("source"):
                        score = parsed_result["source"]["score"]
                        doc_id = parsed_result["source"]["doc_id"]
                        print(f"✅ Answer found - Score: {score:.4f}, Doc: {doc_id}")
                    else:
                        print("⚠️  Insufficient source - No reliable answer")
                        
                except json.JSONDecodeError:
                    print("Response (non-JSON):")
                    print(result)
            else:
                print("Response (body returned):")
                print(json.dumps(result, ensure_ascii=False, indent=2))
                
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
        
        print("-" * 60)

async def test_direct_search():
    """Test the search system directly"""
    
    print("\nDirect Search System Test")
    print("=" * 30)
    
    try:
        # Get search system
        system = get_search_system()
        
        # Test queries
        queries = [
            "What is Sentry?",
            "How does ClickHouse work?", 
            "What is machine learning?",
            "How to cook pasta?"
        ]
        
        for query in queries:
            print(f"\nQuery: {query}")
            result = system.search(query)
            
            if result["source"]:
                score = result["source"]["score"]
                doc_id = result["source"]["doc_id"]
                print(f"✅ Score: {score:.4f} | Doc: {doc_id}")
                print(f"Answer: {result['answer'][:100]}...")
            else:
                print("⚠️  Insufficient source")
                print(f"Answer: {result['answer']}")
                
    except Exception as e:
        print(f"❌ Direct search error: {e}")
        import traceback
        traceback.print_exc()

def test_system_initialization():
    """Test if the system initializes correctly"""
    
    print("System Initialization Test")
    print("=" * 30)
    
    try:
        # Check if documents exist
        docs_path = Path("./documents")
        if not docs_path.exists():
            print("❌ Documents folder not found")
            return False
            
        # List documents
        doc_files = list(docs_path.glob("*.pdf")) + list(docs_path.glob("*.txt"))
        print(f"Found {len(doc_files)} document files:")
        for doc_file in doc_files:
            print(f"  - {doc_file.name}")
        
        if not doc_files:
            print("❌ No documents found in ./documents folder")
            return False
        
        # Test system initialization
        system = get_search_system()
        
        if system.embeddings is None:
            print("❌ System not initialized - no embeddings")
            return False
            
        print(f"✅ System initialized successfully")
        print(f"   - {len(system.chunks)} chunks created")
        print(f"   - Embeddings shape: {system.embeddings.shape}")
        print(f"   - Model: {system.model_name}")
        print(f"   - Threshold: {system.threshold}")
        
        return True
        
    except Exception as e:
        print(f"❌ Initialization error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    
    print("Open WebUI Document Embedding Search - Function Test")
    print("=" * 70)
    
    # Test 1: System initialization
    if not test_system_initialization():
        print("\n❌ System initialization failed. Cannot proceed with tests.")
        return
    
    # Test 2: Direct search
    await test_direct_search()
    
    # Test 3: Open WebUI function
    await test_openwebui_function()
    
    print("\n" + "=" * 70)
    print("Test Summary:")
    print("✅ If all tests passed, the function is ready for Open WebUI")
    print("✅ You can now integrate it with your Docker setup")
    print("\nNext steps:")
    print("1. Follow OPENWEBUI_SETUP.md instructions")
    print("2. Mount the function file in your Docker container")
    print("3. Test in Open WebUI web interface")

if __name__ == "__main__":
    asyncio.run(main())
