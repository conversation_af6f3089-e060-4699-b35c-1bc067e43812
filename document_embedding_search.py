#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Document Embedding Search System

When a query comes in:
1. Search the document embedding index
2. Compute similarity scores (cosine) for each chunk
3. Sort results by score in descending order
4. Select only the single chunk with the highest score
5. If the top score < 0.70: respond with "Insufficient source — no reliable answer from available documents."
6. Base the answer only on the selected chunk; do not hallucinate or use internal model knowledge
7. Keep the answer short and clear; then show the source info: {doc_id, chunk_id, score, excerpt}
8. Output format must be JSON

Usage:
    python document_embedding_search.py --docs-folder ./documents --query "What is Sentry?"
    python document_embedding_search.py --docs-folder ./documents --query "How does ClickHouse work?" --model "Qwen/Qwen2-1.5B-Instruct"
"""

import argparse
import json
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import numpy as np

# PDF reading
from pypdf import PdfReader

# Embedding model
from sentence_transformers import SentenceTransformer

# Text processing
import re

# For LLM-based answer generation
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    import torch
    HAS_TRANSFORMERS = True
except ImportError:
    HAS_TRANSFORMERS = False

# ----------------------------
# 1) Document Loading
# ----------------------------

def read_text(path: Path) -> str:
    """Read text from .txt or .pdf files"""
    if not path.exists():
        raise FileNotFoundError(f"File not found: {path}")

    if path.suffix.lower() == ".txt":
        return path.read_text(encoding="utf-8", errors="ignore")

    if path.suffix.lower() == ".pdf":
        reader = PdfReader(str(path))
        texts = []
        for page in reader.pages:
            text = page.extract_text() or ""
            texts.append(text)
        return "\n".join(texts)

    raise ValueError(f"Unsupported file type: {path.suffix} (only .txt and .pdf supported)")

def load_documents(docs_path: str) -> Dict[str, str]:
    """Load multiple documents from folder or single file"""
    docs_path = Path(docs_path)
    documents = {}

    if docs_path.is_file():
        # Single file
        doc_id = docs_path.stem
        text = read_text(docs_path)
        documents[doc_id] = text
        print(f"Loaded: {doc_id} ({len(text)} characters)")

    elif docs_path.is_dir():
        # All PDF/TXT files in folder
        patterns = ["*.pdf", "*.txt"]
        for pattern in patterns:
            for file_path in docs_path.glob(pattern):
                doc_id = file_path.stem
                try:
                    text = read_text(file_path)
                    documents[doc_id] = text
                    print(f"Loaded: {doc_id} ({len(text)} characters)")
                except Exception as e:
                    print(f"Error loading {file_path}: {e}")
    else:
        raise ValueError(f"Invalid path: {docs_path}")

    return documents

# ----------------------------
# 2) Text Chunking
# ----------------------------

_SENT_SPLIT = re.compile(r'(?<=[.!?])\s+')

def chunk_text(text: str, chunk_size_words: int = 300, overlap_words: int = 50, min_chars: int = 120) -> Tuple[List[str], List[int]]:
    """
    Sentence-aware chunking that respects sentence boundaries
    """
    # Split into sentences
    sents = [s.strip() for s in _SENT_SPLIT.split(text) if s.strip()]
    chunks, starts = [], []
    current_chunk = []
    current_word_count = 0
    word_position = 0

    for sent in sents:
        sent_words = sent.split()
        sent_word_count = len(sent_words)

        # If adding this sentence exceeds chunk size
        if current_word_count + sent_word_count > chunk_size_words and current_chunk:
            # Save current chunk
            chunk_text = " ".join(current_chunk).strip()
            if len(chunk_text) >= min_chars:
                chunks.append(chunk_text)
                starts.append(word_position - current_word_count)

            # Start new chunk with overlap
            if overlap_words > 0 and current_chunk:
                overlap_sents = []
                temp_words = 0
                for i in range(len(current_chunk) - 1, -1, -1):
                    sent_words_count = len(current_chunk[i].split())
                    if temp_words + sent_words_count <= overlap_words:
                        overlap_sents.insert(0, current_chunk[i])
                        temp_words += sent_words_count
                    else:
                        break
                current_chunk = overlap_sents
                current_word_count = temp_words
            else:
                current_chunk = []
                current_word_count = 0

        # Add sentence
        current_chunk.append(sent)
        current_word_count += sent_word_count
        word_position += sent_word_count

    # Add final chunk
    if current_chunk:
        chunk_text = " ".join(current_chunk).strip()
        if len(chunk_text) >= min_chars:
            chunks.append(chunk_text)
            starts.append(word_position - current_word_count)

    return chunks, starts

def process_documents(documents: Dict[str, str], chunk_size_words: int = 300, overlap_words: int = 50) -> Tuple[List[str], List[Dict]]:
    """
    Process all documents into chunks with metadata
    """
    all_chunks = []
    all_metas = []

    for doc_id, text in documents.items():
        chunks, starts = chunk_text(text, chunk_size_words, overlap_words)

        for i, (chunk, start) in enumerate(zip(chunks, starts)):
            all_chunks.append(chunk)
            all_metas.append({
                "doc_id": doc_id,
                "chunk_id": len(all_metas),  # Global chunk ID
                "local_chunk_id": i,         # Document-local chunk ID
                "start_word_index": start,
            })

    return all_chunks, all_metas

# ----------------------------
# 3) Embedding and Search
# ----------------------------

class DocumentEmbeddingSearch:
    def __init__(self, model_name: str = "intfloat/multilingual-e5-base", threshold: float = 0.70,
                 llm_model: Optional[str] = None):
        """
        Initialize the document embedding search system

        Args:
            model_name: Name of the embedding model to use
            threshold: Minimum similarity threshold for reliable answers
            llm_model: Optional LLM model for answer generation (e.g., "Qwen/Qwen2-1.5B-Instruct")
        """
        self.model_name = model_name
        self.threshold = threshold
        self.model = SentenceTransformer(model_name)
        self.chunks = []
        self.metas = []
        self.embeddings = None

        # Initialize LLM for answer generation if specified
        self.llm_model = llm_model
        self.llm_tokenizer = None
        self.llm = None
        if llm_model and HAS_TRANSFORMERS:
            try:
                print(f"Loading LLM model: {llm_model}")
                self.llm_tokenizer = AutoTokenizer.from_pretrained(llm_model)
                self.llm = AutoModelForCausalLM.from_pretrained(
                    llm_model,
                    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                    device_map="auto" if torch.cuda.is_available() else None
                )
                print(f"LLM model loaded successfully")
            except Exception as e:
                print(f"Warning: Could not load LLM model {llm_model}: {e}")
                self.llm_model = None
        
    def index_documents(self, documents: Dict[str, str], chunk_size_words: int = 300, overlap_words: int = 50):
        """
        Index documents by creating chunks and embeddings
        """
        print(f"Processing documents with model: {self.model_name}")
        
        # Process documents into chunks
        self.chunks, self.metas = process_documents(documents, chunk_size_words, overlap_words)
        print(f"Created {len(self.chunks)} chunks from {len(documents)} documents")
        
        # Create embeddings
        print("Creating embeddings...")
        # Use E5 format for passages
        passages = [f"passage: {chunk}" for chunk in self.chunks]
        self.embeddings = self.model.encode(passages, normalize_embeddings=True, show_progress_bar=True)
        self.embeddings = self.embeddings.astype("float32")
        
        print(f"Embeddings shape: {self.embeddings.shape}")
        
    def search(self, query: str) -> Dict:
        """
        Search for the most relevant chunk and return JSON response
        
        Args:
            query: Search query
            
        Returns:
            JSON response with answer and source information
        """
        if self.embeddings is None or len(self.chunks) == 0:
            return {
                "answer": "No documents indexed",
                "source": None
            }
        
        # Encode query with E5 format
        query_embedding = self.model.encode([f"query: {query}"], normalize_embeddings=True)
        query_embedding = query_embedding.astype("float32")[0]  # Get single vector
        
        # Compute cosine similarity scores for all chunks
        similarity_scores = self.embeddings @ query_embedding  # Cosine similarity (normalized vectors)
        
        # Sort results by score in descending order
        sorted_indices = np.argsort(similarity_scores)[::-1]
        
        # Select only the single chunk with the highest score
        top_index = sorted_indices[0]
        top_score = float(similarity_scores[top_index])
        
        # Check if top score meets threshold
        if top_score < self.threshold:
            return {
                "answer": "Insufficient source — no reliable answer from available documents.",
                "source": None
            }
        
        # Get the top chunk and its metadata
        top_chunk = self.chunks[top_index]
        top_meta = self.metas[top_index]
        
        # Create excerpt (first 200 characters)
        excerpt = top_chunk[:200] + "..." if len(top_chunk) > 200 else top_chunk
        
        # Base answer only on the selected chunk
        # Keep answer short and clear - extract key information from the chunk
        answer = self._extract_answer(query, top_chunk)
        
        return {
            "answer": answer,
            "source": {
                "doc_id": top_meta["doc_id"],
                "chunk_id": top_meta["chunk_id"],
                "score": round(top_score, 4),
                "excerpt": excerpt
            }
        }
    
    def _extract_answer(self, query: str, chunk: str) -> str:
        """
        Extract a concise answer from the chunk based on the query
        Uses LLM if available, otherwise falls back to simple extraction
        """
        # If LLM is available, use it to generate a focused answer
        if self.llm and self.llm_tokenizer:
            return self._generate_llm_answer(query, chunk)

        # Fallback: simple extraction
        sentences = [s.strip() for s in _SENT_SPLIT.split(chunk) if s.strip()]

        if not sentences:
            return chunk[:200] + "..." if len(chunk) > 200 else chunk

        # Return first 1-2 sentences as answer
        if len(sentences) == 1:
            return sentences[0]
        else:
            answer = sentences[0]
            if len(answer) < 100 and len(sentences) > 1:
                answer += " " + sentences[1]
            return answer

    def _generate_llm_answer(self, query: str, chunk: str) -> str:
        """
        Generate answer using LLM based on the query and chunk
        """
        try:
            # Create prompt for answer generation
            prompt = f"""Based on the following text, answer the question concisely and accurately. Only use information from the provided text.

Text: {chunk}

Question: {query}

Answer:"""

            # Tokenize and generate
            inputs = self.llm_tokenizer(prompt, return_tensors="pt", truncation=True, max_length=1024)

            # Move to same device as model
            if hasattr(self.llm, 'device'):
                inputs = {k: v.to(self.llm.device) for k, v in inputs.items()}

            with torch.no_grad():
                outputs = self.llm.generate(
                    **inputs,
                    max_new_tokens=150,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.llm_tokenizer.eos_token_id
                )

            # Decode the generated answer
            generated_text = self.llm_tokenizer.decode(outputs[0], skip_special_tokens=True)

            # Extract only the answer part (after "Answer:")
            if "Answer:" in generated_text:
                answer = generated_text.split("Answer:")[-1].strip()
                # Clean up the answer - take only the first paragraph/sentence
                answer_lines = answer.split('\n')
                clean_answer = answer_lines[0].strip()
                return clean_answer if clean_answer else answer.strip()

            return generated_text.strip()

        except Exception as e:
            print(f"Warning: LLM generation failed: {e}")
            # Fallback to simple extraction
            sentences = [s.strip() for s in _SENT_SPLIT.split(chunk) if s.strip()]
            return sentences[0] if sentences else chunk[:200]

# ----------------------------
# 4) Main Function
# ----------------------------

def parse_args():
    parser = argparse.ArgumentParser(description="Document Embedding Search System")
    parser.add_argument("--docs-folder", required=True, type=str, help="Path to documents folder or single file")
    parser.add_argument("--query", required=True, type=str, help="Search query")
    parser.add_argument("--model", type=str, default="intfloat/multilingual-e5-base", help="Embedding model name")
    parser.add_argument("--llm-model", type=str, default=None, help="LLM model for answer generation (e.g., Qwen/Qwen2-1.5B-Instruct)")
    parser.add_argument("--threshold", type=float, default=0.70, help="Minimum similarity threshold")
    parser.add_argument("--chunk-size", type=int, default=300, help="Chunk size in words")
    parser.add_argument("--overlap", type=int, default=50, help="Overlap between chunks in words")
    return parser.parse_args()

def main():
    args = parse_args()
    
    try:
        # Load documents
        documents = load_documents(args.docs_folder)
        if not documents:
            print("Error: No documents found", file=sys.stderr)
            sys.exit(1)
        
        # Initialize search system
        search_system = DocumentEmbeddingSearch(
            model_name=args.model,
            threshold=args.threshold,
            llm_model=args.llm_model
        )
        
        # Index documents
        search_system.index_documents(documents, chunk_size_words=args.chunk_size, overlap_words=args.overlap)
        
        # Perform search
        result = search_system.search(args.query)
        
        # Output JSON result
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
    except Exception as e:
        error_result = {
            "answer": f"Error: {str(e)}",
            "source": None
        }
        print(json.dumps(error_result, ensure_ascii=False, indent=2))
        sys.exit(1)

if __name__ == "__main__":
    main()
