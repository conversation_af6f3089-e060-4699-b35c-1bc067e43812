"""
title: Advanced RAG with Hybrid Retrieval
author: OpenWebUI RAG Integration
author_url: https://github.com/open-webui
funding_url: https://github.com/sponsors/open-webui
version: 1.0.0
license: MIT
requirements: faiss-cpu>=1.7.4,numpy>=1.24.0,sentence-transformers>=2.2.2,rank-bm25>=0.2.2,torch>=2.0.0,scikit-learn>=1.3.0,transformers>=4.40.0,pypdf>=3.9.0
"""

import os
import json
import logging
import traceback
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

import numpy as np
import faiss
from sentence_transformers import SentenceTransformer, CrossEncoder
from rank_bm25 import BM25Okapi
from pypdf import PdfReader

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OpenWebUIRAGError(Exception):
    """Custom exception for RAG-related errors"""
    pass

class AdvancedRAGRetriever:
    """
    Advanced RAG system with hybrid retrieval (BM25 + Dense FAISS) + RRF fusion
    Integrates with existing Python embedding system for OpenWebUI
    """
    
    def __init__(self, 
                 documents_path: str = "./documents",
                 dense_model: str = "intfloat/multilingual-e5-base",
                 chunk_size: int = 300,
                 overlap: int = 50,
                 similarity_threshold: float = 0.85,
                 max_per_doc: int = 3):
        """
        Initialize the RAG retriever with configuration
        
        Args:
            documents_path: Path to documents folder
            dense_model: SentenceTransformer model name
            chunk_size: Chunk size in words
            overlap: Overlap between chunks in words
            similarity_threshold: Threshold for similarity detection
            max_per_doc: Maximum chunks per document
        """
        self.documents_path = Path(documents_path)
        self.dense_model_name = dense_model
        self.chunk_size = chunk_size
        self.overlap = overlap
        self.similarity_threshold = similarity_threshold
        self.max_per_doc = max_per_doc
        
        # Initialize components
        self.dense_model = None
        self.index = None
        self.embeddings = None
        self.bm25 = None
        self.chunks = []
        self.metas = []
        self.duplicate_groups = []
        
        # Load and initialize if documents exist
        if self.documents_path.exists():
            self._initialize_system()
    
    def _initialize_system(self):
        """Initialize the RAG system with existing documents"""
        try:
            logger.info("Initializing RAG system...")
            
            # Load documents
            documents = self._load_multiple_documents()
            if not documents:
                logger.warning("No documents found to initialize RAG system")
                return
            
            # Process documents into chunks
            self.chunks, self.metas = self._process_all_documents(documents)
            logger.info(f"Created {len(self.chunks)} chunks from {len(documents)} documents")
            
            # Initialize dense model and embeddings
            self.dense_model = SentenceTransformer(self.dense_model_name)
            self.embeddings = self.dense_model.encode(
                [f"passage: {c}" for c in self.chunks], 
                normalize_embeddings=True
            ).astype("float32")
            
            # Build FAISS index
            self.index = faiss.IndexFlatIP(self.embeddings.shape[1])
            self.index.add(self.embeddings)
            
            # Build BM25 index
            self.bm25 = BM25Okapi([c.lower().split() for c in self.chunks])
            
            # Detect similar chunks
            self.duplicate_groups = self._detect_and_group_duplicates()
            
            logger.info("RAG system initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize RAG system: {e}")
            raise OpenWebUIRAGError(f"Initialization failed: {e}")
    
    def _load_multiple_documents(self) -> Dict[str, str]:
        """Load multiple documents from the documents path"""
        documents = {}
        
        if self.documents_path.is_file():
            # Single file
            doc_id = self.documents_path.stem
            text = self._read_text(self.documents_path)
            documents[doc_id] = text
            logger.info(f"Loaded: {doc_id} ({len(text)} characters)")
            
        elif self.documents_path.is_dir():
            # Multiple files in directory
            patterns = ["*.pdf", "*.txt"]
            for pattern in patterns:
                for file_path in self.documents_path.glob(pattern):
                    doc_id = file_path.stem
                    try:
                        text = self._read_text(file_path)
                        documents[doc_id] = text
                        logger.info(f"Loaded: {doc_id} ({len(text)} characters)")
                    except Exception as e:
                        logger.error(f"Error loading {file_path}: {e}")
        
        return documents
    
    def _read_text(self, path: Path) -> str:
        """Read text from .txt or .pdf file"""
        if not path.exists():
            raise FileNotFoundError(f"File not found: {path}")
        
        if path.suffix.lower() == ".txt":
            return path.read_text(encoding="utf-8", errors="ignore")
        
        if path.suffix.lower() == ".pdf":
            reader = PdfReader(str(path))
            texts = []
            for page in reader.pages:
                text = page.extract_text() or ""
                texts.append(text)
            return "\n".join(texts)
        
        raise ValueError(f"Unsupported file type: {path.suffix} (only .txt and .pdf supported)")
    
    def retrieve_context(self, query: str, final_k: int = 5, topk: int = 50) -> List[Dict[str, Any]]:
        """
        Retrieve relevant context for a query using hybrid retrieval
        
        Args:
            query: User query
            final_k: Number of final results to return
            topk: Number of candidates for initial retrieval
            
        Returns:
            List of context chunks with metadata
        """
        if not self._is_initialized():
            logger.warning("RAG system not initialized, returning empty context")
            return []
        
        try:
            # Perform hybrid retrieval
            dense_results = self._dense_search(query, topk)
            bm25_results = self._bm25_search(query, topk)
            
            # RRF fusion
            fusion_results = self._rrf([dense_results, bm25_results], k=topk)
            candidate_ids = [chunk_id for chunk_id, score in fusion_results[:topk]
                           if 0 <= chunk_id < len(self.chunks)]
            
            # Apply similarity penalty and document diversity
            candidate_ids = self._apply_filters(candidate_ids)
            
            # MMR diversity selection
            candidate_ids = self._mmr_select(query, candidate_ids, min(topk, len(candidate_ids)))
            
            # Semantic reranking
            final_ids, scores = self._semantic_rerank(query, candidate_ids, final_k)
            
            # Build context results
            context = []
            for i, chunk_id in enumerate(final_ids):
                if 0 <= chunk_id < len(self.chunks):
                    context.append({
                        "text": self.chunks[chunk_id],
                        "metadata": self.metas[chunk_id],
                        "score": scores[i] if i < len(scores) else 0.0
                    })
            
            logger.info(f"Retrieved {len(context)} context chunks for query: {query[:50]}...")
            return context
            
        except Exception as e:
            logger.error(f"Error retrieving context: {e}")
            return []
    
    def _is_initialized(self) -> bool:
        """Check if the RAG system is properly initialized"""
        return (self.dense_model is not None and
                self.index is not None and
                self.embeddings is not None and
                self.bm25 is not None and
                len(self.chunks) > 0)

    def _process_all_documents(self, documents: Dict[str, str]) -> Tuple[List[str], List[Dict[str, Any]]]:
        """Process all documents into chunks and metadata"""
        import re

        all_chunks = []
        all_metas = []
        today = datetime.now().date().isoformat()

        # Sentence splitting regex
        sent_split = re.compile(r'(?<=[.!?])\s+')

        for doc_id, text in documents.items():
            chunks, starts = self._chunk_text(text, sent_split)

            for i, (chunk, start) in enumerate(zip(chunks, starts)):
                all_chunks.append(chunk)
                all_metas.append({
                    "doc_id": doc_id,
                    "chunk_id": len(all_metas),
                    "local_chunk_id": i,
                    "start_word_index": start,
                    "date": today,
                })

        return all_chunks, all_metas

    def _chunk_text(self, text: str, sent_split, min_chars: int = 120) -> Tuple[List[str], List[int]]:
        """Chunk text respecting sentence boundaries"""
        # Split into sentences
        sents = [s.strip() for s in sent_split.split(text) if s.strip()]
        chunks, starts = [], []
        current_chunk = []
        current_word_count = 0
        word_position = 0

        for sent in sents:
            sent_words = sent.split()
            sent_word_count = len(sent_words)

            # If adding this sentence exceeds chunk size
            if current_word_count + sent_word_count > self.chunk_size and current_chunk:
                # Save current chunk
                chunk_text = " ".join(current_chunk).strip()
                if len(chunk_text) >= min_chars:
                    chunks.append(chunk_text)
                    starts.append(word_position - current_word_count)

                # Start new chunk with overlap
                if self.overlap > 0 and current_chunk:
                    overlap_sents = []
                    temp_words = 0
                    for i in range(len(current_chunk) - 1, -1, -1):
                        sent_words_count = len(current_chunk[i].split())
                        if temp_words + sent_words_count <= self.overlap:
                            overlap_sents.insert(0, current_chunk[i])
                            temp_words += sent_words_count
                        else:
                            break
                    current_chunk = overlap_sents
                    current_word_count = temp_words
                else:
                    current_chunk = []
                    current_word_count = 0

            # Add sentence
            current_chunk.append(sent)
            current_word_count += sent_word_count
            word_position += sent_word_count

        # Add final chunk
        if current_chunk:
            chunk_text = " ".join(current_chunk).strip()
            if len(chunk_text) >= min_chars:
                chunks.append(chunk_text)
                starts.append(word_position - current_word_count)

        return chunks, starts

    def _detect_and_group_duplicates(self) -> List[List[int]]:
        """Detect and group similar chunks"""
        similarity_matrix = self.embeddings @ self.embeddings.T
        similar_pairs = []

        for i in range(len(self.embeddings)):
            for j in range(i+1, len(self.embeddings)):
                similarity = similarity_matrix[i, j]
                if similarity > self.similarity_threshold:
                    similar_pairs.append((i, j, similarity))

        # Create groups
        groups = []
        processed = set()

        for i, j, sim in similar_pairs:
            if i in processed or j in processed:
                continue

            group = {i, j}
            for x, y, _ in similar_pairs:
                if x in group or y in group:
                    group.add(x)
                    group.add(y)

            groups.append(list(group))
            processed.update(group)

        return groups

    def _dense_search(self, query: str, topk: int = 30) -> List[Tuple[int, float]]:
        """Perform dense vector search"""
        q_vec = self.dense_model.encode([f"query: {query}"], normalize_embeddings=True).astype("float32")
        D, I = self.index.search(q_vec, topk)
        return list(zip(I[0].tolist(), D[0].tolist()))

    def _bm25_search(self, query: str, topk: int = 30) -> List[Tuple[int, float]]:
        """Perform BM25 lexical search"""
        scores = self.bm25.get_scores(query.lower().split())
        I = np.argsort(scores)[::-1][:topk]
        return list(zip(I.tolist(), scores[I].tolist()))

    def _rrf(self, results_list: List[List[Tuple[int, float]]], k: int = 60, c: int = 60) -> List[Tuple[int, float]]:
        """Reciprocal Rank Fusion"""
        chunk_scores = {}

        for results in results_list:
            for rank, (chunk_id, score) in enumerate(results[:k], start=1):
                if chunk_id not in chunk_scores:
                    chunk_scores[chunk_id] = 0
                chunk_scores[chunk_id] += 1.0 / (c + rank)

        return sorted(chunk_scores.items(), key=lambda x: x[1], reverse=True)

    def _apply_filters(self, candidate_ids: List[int]) -> List[int]:
        """Apply similarity penalty and document diversity filters"""
        # Apply similarity penalty
        if self.duplicate_groups:
            chunk_to_group = {}
            for group_id, chunk_ids in enumerate(self.duplicate_groups):
                for chunk_id in chunk_ids:
                    chunk_to_group[chunk_id] = group_id

            group_counts = {}
            filtered_ids = []

            for chunk_id in candidate_ids:
                if chunk_id in chunk_to_group:
                    group_id = chunk_to_group[chunk_id]
                    group_counts[group_id] = group_counts.get(group_id, 0) + 1
                    # Apply penalty for duplicate groups
                    if group_counts[group_id] <= 2:  # Allow max 2 per group
                        filtered_ids.append(chunk_id)
                else:
                    filtered_ids.append(chunk_id)

            candidate_ids = filtered_ids

        # Apply document diversity
        doc_counts = {}
        diverse_ids = []

        for chunk_id in candidate_ids:
            if 0 <= chunk_id < len(self.metas):
                doc_id = self.metas[chunk_id]['doc_id']
                doc_counts[doc_id] = doc_counts.get(doc_id, 0) + 1

                if doc_counts[doc_id] <= self.max_per_doc:
                    diverse_ids.append(chunk_id)

        return diverse_ids

    def _mmr_select(self, query: str, candidate_ids: List[int], k: int, lambda_mult: float = 0.7) -> List[int]:
        """Maximum Marginal Relevance selection for diversity"""
        if not candidate_ids or k <= 0:
            return []

        # Get query vector
        query_vec = self.dense_model.encode([f"query: {query}"], normalize_embeddings=True).astype("float32")
        if len(query_vec.shape) == 2:
            query_vec = query_vec.squeeze()

        selected = []
        remaining_cands = candidate_ids[:]

        # Query similarities for all candidates
        query_sims = self.embeddings[remaining_cands] @ query_vec

        while remaining_cands and len(selected) < k:
            if not selected:
                # First selection: highest query similarity
                best_idx = int(np.argmax(query_sims))
            else:
                # MMR calculation
                selected_embs = self.embeddings[selected]
                max_similarities = []

                for i, cand_id in enumerate(remaining_cands):
                    cand_emb = self.embeddings[cand_id]
                    sims_with_selected = selected_embs @ cand_emb
                    max_sim = np.max(sims_with_selected)
                    max_similarities.append(max_sim)

                max_similarities = np.array(max_similarities)
                mmr_scores = lambda_mult * query_sims - (1 - lambda_mult) * max_similarities
                best_idx = int(np.argmax(mmr_scores))

            # Select best candidate
            selected_cand = remaining_cands[best_idx]
            selected.append(selected_cand)

            # Remove from remaining
            remaining_cands.pop(best_idx)
            query_sims = np.delete(query_sims, best_idx)

        return selected

    def _semantic_rerank(self, query: str, candidate_ids: List[int], final_k: int = 5) -> Tuple[List[int], List[float]]:
        """Semantic reranking using sentence transformer similarity"""
        # Filter valid and unique chunk IDs
        valid_unique_ids = []
        seen = set()
        for cid in candidate_ids:
            if 0 <= cid < len(self.chunks) and cid not in seen:
                valid_unique_ids.append(cid)
                seen.add(cid)

        if not valid_unique_ids:
            return [], []

        # Query embedding
        query_emb = self.dense_model.encode([f"query: {query}"], normalize_embeddings=True).astype("float32")

        # Calculate similarities
        scores = []
        for cid in valid_unique_ids:
            chunk_emb = self.embeddings[cid:cid+1]
            similarity = float(chunk_emb @ query_emb.T)
            scores.append(similarity)

        # Sort by score
        ranked = sorted(zip(valid_unique_ids, scores), key=lambda x: x[1], reverse=True)
        top_k = ranked[:final_k]

        top_ids = [cid for cid, _ in top_k]
        top_scores = [score for _, score in top_k]

        return top_ids, top_scores

# Global RAG retriever instance
rag_retriever = None

def get_rag_retriever() -> AdvancedRAGRetriever:
    """Get or create the global RAG retriever instance"""
    global rag_retriever
    if rag_retriever is None:
        # Get configuration from environment variables
        documents_path = os.getenv("RAG_DOCUMENTS_PATH", "./documents")
        dense_model = os.getenv("RAG_DENSE_MODEL", "intfloat/multilingual-e5-base")
        chunk_size = int(os.getenv("RAG_CHUNK_SIZE", "300"))
        overlap = int(os.getenv("RAG_OVERLAP", "50"))
        
        rag_retriever = AdvancedRAGRetriever(
            documents_path=documents_path,
            dense_model=dense_model,
            chunk_size=chunk_size,
            overlap=overlap
        )
    return rag_retriever

def enhance_prompt_with_context(original_prompt: str, context: List[Dict[str, Any]]) -> str:
    """
    Enhance the original prompt with retrieved context
    
    Args:
        original_prompt: Original user prompt
        context: Retrieved context chunks
        
    Returns:
        Enhanced prompt with context
    """
    if not context:
        return original_prompt
    
    # Build context section
    context_text = "\n\n".join([
        f"[Document: {ctx['metadata']['doc_id']}]\n{ctx['text']}"
        for ctx in context
    ])
    
    # Create enhanced prompt
    enhanced_prompt = f"""Based on the following context information, please answer the question. If the context doesn't contain relevant information, please say so.

CONTEXT:
{context_text}

QUESTION: {original_prompt}

Please provide a comprehensive answer based on the context above."""
    
    return enhanced_prompt

async def pipe(
    body: dict,
    __user__: Optional[dict] = None,
    __event_emitter__=None,
    __task__: Optional[str] = None,
) -> str:
    """
    Main OpenWebUI function pipe for RAG-enhanced responses
    
    Args:
        body: Request body containing messages and model info
        __user__: User information (optional)
        __event_emitter__: Event emitter for streaming (optional)
        __task__: Task identifier (optional)
        
    Returns:
        Enhanced prompt or error message
    """
    try:
        # Extract the user's message
        messages = body.get("messages", [])
        if not messages:
            return "No messages provided"
        
        # Get the last user message
        user_message = None
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break
        
        if not user_message:
            return "No user message found"
        
        # Get RAG retriever
        retriever = get_rag_retriever()
        
        # Retrieve relevant context
        context = retriever.retrieve_context(user_message, final_k=5)
        
        # Enhance prompt with context
        enhanced_prompt = enhance_prompt_with_context(user_message, context)
        
        # Update the last user message with enhanced prompt
        for i in range(len(messages) - 1, -1, -1):
            if messages[i].get("role") == "user":
                messages[i]["content"] = enhanced_prompt
                break
        
        # Emit status if event emitter is available
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {
                    "description": f"Enhanced prompt with {len(context)} relevant context chunks",
                    "done": True
                }
            })
        
        return body
        
    except Exception as e:
        error_msg = f"RAG Enhancement Error: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        
        if __event_emitter__:
            await __event_emitter__({
                "type": "status", 
                "data": {
                    "description": error_msg,
                    "done": True
                }
            })
        
        return body  # Return original body on error
