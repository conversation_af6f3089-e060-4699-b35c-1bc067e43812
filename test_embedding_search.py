#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for Document Embedding Search System

This script demonstrates the document embedding search functionality
with sample queries and shows the JSON output format.
"""

import json
import sys
from pathlib import Path

# Import our search system
from document_embedding_search import DocumentEmbeddingSearch, load_documents

def test_search_system():
    """Test the document embedding search system with sample data"""
    
    # Check if documents exist
    docs_folder = Path("./documents")
    if not docs_folder.exists():
        print("Error: ./documents folder not found")
        print("Please ensure you have documents in the ./documents folder")
        return
    
    # Sample queries to test
    test_queries = [
        "What is Sentry?",
        "How does ClickHouse work?",
        "What are the performance benefits?",
        "Explain payment integration",
        "What is the architecture?",
        "This is a completely unrelated query about quantum physics"  # Should trigger threshold
    ]
    
    try:
        # Load documents
        print("Loading documents...")
        documents = load_documents(str(docs_folder))
        if not documents:
            print("No documents found in ./documents folder")
            return
        
        print(f"Loaded {len(documents)} documents")
        
        # Initialize search system with default embedding model
        print("\n=== Testing with default embedding model ===")
        search_system = DocumentEmbeddingSearch(
            model_name="intfloat/multilingual-e5-base",
            threshold=0.70
        )
        
        # Index documents
        search_system.index_documents(documents)
        
        # Test each query
        for i, query in enumerate(test_queries, 1):
            print(f"\n--- Test Query {i}: {query} ---")
            result = search_system.search(query)
            print(json.dumps(result, ensure_ascii=False, indent=2))
        
        # Test with Qwen model if available
        print("\n\n=== Testing with Qwen2-1.5B-Instruct for answer generation ===")
        try:
            search_system_qwen = DocumentEmbeddingSearch(
                model_name="intfloat/multilingual-e5-base",
                threshold=0.70,
                llm_model="Qwen/Qwen2-1.5B-Instruct"
            )
            
            # Index documents
            search_system_qwen.index_documents(documents)
            
            # Test a few queries with LLM-generated answers
            llm_test_queries = test_queries[:3]  # Test first 3 queries
            
            for i, query in enumerate(llm_test_queries, 1):
                print(f"\n--- LLM Test Query {i}: {query} ---")
                result = search_system_qwen.search(query)
                print(json.dumps(result, ensure_ascii=False, indent=2))
                
        except Exception as e:
            print(f"Qwen model test skipped: {e}")
            print("This is normal if the model is not available or requires additional setup")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

def test_threshold_behavior():
    """Test the threshold behavior specifically"""
    print("\n\n=== Testing Threshold Behavior ===")
    
    # Create a simple test document
    test_documents = {
        "test_doc": "Sentry is a monitoring platform that helps developers track errors and performance issues. ClickHouse is a columnar database management system for online analytical processing."
    }
    
    # Test with different thresholds
    thresholds = [0.5, 0.7, 0.9]
    query = "What is machine learning?"  # Unrelated query
    
    for threshold in thresholds:
        print(f"\n--- Testing threshold {threshold} ---")
        search_system = DocumentEmbeddingSearch(threshold=threshold)
        search_system.index_documents(test_documents)
        result = search_system.search(query)
        print(f"Threshold {threshold}: {result['answer']}")

def main():
    """Main test function"""
    print("Document Embedding Search System - Test Suite")
    print("=" * 60)
    
    # Test main functionality
    test_search_system()
    
    # Test threshold behavior
    test_threshold_behavior()
    
    print("\n" + "=" * 60)
    print("Test suite completed!")
    print("\nUsage examples:")
    print("python document_embedding_search.py --docs-folder ./documents --query 'What is Sentry?'")
    print("python document_embedding_search.py --docs-folder ./documents --query 'How does ClickHouse work?' --llm-model 'Qwen/Qwen2-1.5B-Instruct'")

if __name__ == "__main__":
    main()
