"""
title: Document Embedding Search
author: OpenWebUI Integration
author_url: https://github.com/open-webui
funding_url: https://github.com/sponsors/open-webui
version: 1.0.0
license: MIT
requirements: faiss-cpu>=1.7.4,numpy>=1.24.0,sentence-transformers>=2.2.2,pypdf>=3.9.0
"""

import os
import json
import logging
import traceback
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from sentence_transformers import SentenceTransformer
from pypdf import PdfReader
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DocumentEmbeddingSearchOpenWebUI:
    """
    Document Embedding Search for OpenWebUI
    
    Implements exact specifications:
    1. Search document embedding index
    2. Compute cosine similarity scores for each chunk
    3. Sort results by score in descending order
    4. Select only the single chunk with the highest score
    5. If top score < 0.70: respond with "Insufficient source" message
    6. Base answer only on selected chunk; no hallucination
    7. Keep answer short and clear; show source info
    8. Output format must be JSON
    """
    
    def __init__(self, 
                 documents_path: str = "./documents",
                 model_name: str = "intfloat/multilingual-e5-base",
                 threshold: float = 0.70,
                 chunk_size: int = 300,
                 overlap: int = 50):
        """Initialize the document embedding search system"""
        self.documents_path = Path(documents_path)
        self.model_name = model_name
        self.threshold = threshold
        self.chunk_size = chunk_size
        self.overlap = overlap
        
        # Initialize components
        self.model = None
        self.chunks = []
        self.metas = []
        self.embeddings = None
        
        # Initialize if documents exist
        if self.documents_path.exists():
            self._initialize_system()
    
    def _initialize_system(self):
        """Initialize the search system with documents"""
        try:
            logger.info("Initializing Document Embedding Search...")
            
            # Load documents
            documents = self._load_documents()
            if not documents:
                logger.warning("No documents found")
                return
            
            # Process documents into chunks
            self.chunks, self.metas = self._process_documents(documents)
            logger.info(f"Created {len(self.chunks)} chunks from {len(documents)} documents")
            
            # Initialize model and create embeddings
            self.model = SentenceTransformer(self.model_name)
            passages = [f"passage: {chunk}" for chunk in self.chunks]
            self.embeddings = self.model.encode(passages, normalize_embeddings=True, show_progress_bar=False)
            self.embeddings = self.embeddings.astype("float32")
            
            logger.info(f"Embeddings shape: {self.embeddings.shape}")
            logger.info("Document Embedding Search initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize system: {e}")
            raise
    
    def _load_documents(self) -> Dict[str, str]:
        """Load documents from the documents path"""
        documents = {}
        
        if self.documents_path.is_file():
            # Single file
            doc_id = self.documents_path.stem
            text = self._read_text(self.documents_path)
            documents[doc_id] = text
            logger.info(f"Loaded: {doc_id} ({len(text)} characters)")
            
        elif self.documents_path.is_dir():
            # Multiple files
            patterns = ["*.pdf", "*.txt"]
            for pattern in patterns:
                for file_path in self.documents_path.glob(pattern):
                    doc_id = file_path.stem
                    try:
                        text = self._read_text(file_path)
                        documents[doc_id] = text
                        logger.info(f"Loaded: {doc_id} ({len(text)} characters)")
                    except Exception as e:
                        logger.error(f"Error loading {file_path}: {e}")
        
        return documents
    
    def _read_text(self, path: Path) -> str:
        """Read text from .txt or .pdf file"""
        if not path.exists():
            raise FileNotFoundError(f"File not found: {path}")
        
        if path.suffix.lower() == ".txt":
            return path.read_text(encoding="utf-8", errors="ignore")
        
        if path.suffix.lower() == ".pdf":
            reader = PdfReader(str(path))
            texts = []
            for page in reader.pages:
                text = page.extract_text() or ""
                texts.append(text)
            return "\n".join(texts)
        
        raise ValueError(f"Unsupported file type: {path.suffix}")
    
    def _process_documents(self, documents: Dict[str, str]) -> Tuple[List[str], List[Dict]]:
        """Process documents into chunks with metadata"""
        all_chunks = []
        all_metas = []
        
        for doc_id, text in documents.items():
            chunks, starts = self._chunk_text(text)
            
            for i, (chunk, start) in enumerate(zip(chunks, starts)):
                all_chunks.append(chunk)
                all_metas.append({
                    "doc_id": doc_id,
                    "chunk_id": len(all_metas),
                    "local_chunk_id": i,
                    "start_word_index": start,
                })
        
        return all_chunks, all_metas
    
    def _chunk_text(self, text: str, min_chars: int = 120) -> Tuple[List[str], List[int]]:
        """Chunk text respecting sentence boundaries"""
        sent_split = re.compile(r'(?<=[.!?])\s+')
        sents = [s.strip() for s in sent_split.split(text) if s.strip()]
        chunks, starts = [], []
        current_chunk = []
        current_word_count = 0
        word_position = 0
        
        for sent in sents:
            sent_words = sent.split()
            sent_word_count = len(sent_words)
            
            # If adding this sentence exceeds chunk size
            if current_word_count + sent_word_count > self.chunk_size and current_chunk:
                # Save current chunk
                chunk_text = " ".join(current_chunk).strip()
                if len(chunk_text) >= min_chars:
                    chunks.append(chunk_text)
                    starts.append(word_position - current_word_count)
                
                # Start new chunk with overlap
                if self.overlap > 0 and current_chunk:
                    overlap_sents = []
                    temp_words = 0
                    for i in range(len(current_chunk) - 1, -1, -1):
                        sent_words_count = len(current_chunk[i].split())
                        if temp_words + sent_words_count <= self.overlap:
                            overlap_sents.insert(0, current_chunk[i])
                            temp_words += sent_words_count
                        else:
                            break
                    current_chunk = overlap_sents
                    current_word_count = temp_words
                else:
                    current_chunk = []
                    current_word_count = 0
            
            # Add sentence
            current_chunk.append(sent)
            current_word_count += sent_word_count
            word_position += sent_word_count
        
        # Add final chunk
        if current_chunk:
            chunk_text = " ".join(current_chunk).strip()
            if len(chunk_text) >= min_chars:
                chunks.append(chunk_text)
                starts.append(word_position - current_word_count)
        
        return chunks, starts
    
    def search(self, query: str) -> Dict:
        """
        Search for the most relevant chunk and return JSON response
        
        Follows exact specifications:
        1. Compute cosine similarity scores for each chunk
        2. Sort results by score in descending order
        3. Select only the single chunk with the highest score
        4. If top score < threshold: return "Insufficient source" message
        5. Base answer only on selected chunk
        6. Return JSON format
        """
        if self.embeddings is None or len(self.chunks) == 0:
            return {
                "answer": "No documents indexed",
                "source": None
            }
        
        # Encode query with E5 format
        query_embedding = self.model.encode([f"query: {query}"], normalize_embeddings=True)
        query_embedding = query_embedding.astype("float32")[0]
        
        # Compute cosine similarity scores for all chunks
        similarity_scores = self.embeddings @ query_embedding
        
        # Sort results by score in descending order
        sorted_indices = np.argsort(similarity_scores)[::-1]
        
        # Select only the single chunk with the highest score
        top_index = sorted_indices[0]
        top_score = float(similarity_scores[top_index])
        
        # Check if top score meets threshold
        if top_score < self.threshold:
            return {
                "answer": "Insufficient source — no reliable answer from available documents.",
                "source": None
            }
        
        # Get the top chunk and its metadata
        top_chunk = self.chunks[top_index]
        top_meta = self.metas[top_index]
        
        # Create excerpt (first 200 characters)
        excerpt = top_chunk[:200] + "..." if len(top_chunk) > 200 else top_chunk
        
        # Extract answer from chunk (first 1-2 sentences)
        sent_split = re.compile(r'(?<=[.!?])\s+')
        sentences = [s.strip() for s in sent_split.split(top_chunk) if s.strip()]
        
        if not sentences:
            answer = excerpt
        elif len(sentences) == 1:
            answer = sentences[0]
        else:
            answer = sentences[0]
            if len(answer) < 100 and len(sentences) > 1:
                answer += " " + sentences[1]
        
        return {
            "answer": answer,
            "source": {
                "doc_id": top_meta["doc_id"],
                "chunk_id": top_meta["chunk_id"],
                "score": round(top_score, 4),
                "excerpt": excerpt
            }
        }

# Global search instance
search_system = None

def get_search_system() -> DocumentEmbeddingSearchOpenWebUI:
    """Get or create the global search system instance"""
    global search_system
    if search_system is None:
        # Get configuration from environment variables
        documents_path = os.getenv("EMBEDDING_DOCUMENTS_PATH", "./documents")
        model_name = os.getenv("EMBEDDING_MODEL", "intfloat/multilingual-e5-base")
        threshold = float(os.getenv("EMBEDDING_THRESHOLD", "0.70"))
        chunk_size = int(os.getenv("EMBEDDING_CHUNK_SIZE", "300"))
        overlap = int(os.getenv("EMBEDDING_OVERLAP", "50"))
        
        search_system = DocumentEmbeddingSearchOpenWebUI(
            documents_path=documents_path,
            model_name=model_name,
            threshold=threshold,
            chunk_size=chunk_size,
            overlap=overlap
        )
    return search_system

async def pipe(
    body: dict,
    __user__: Optional[dict] = None,
    __event_emitter__=None,
    __task__: Optional[str] = None,
) -> str:
    """
    Main OpenWebUI function pipe for document embedding search
    
    Returns JSON response with answer and source information
    """
    try:
        # Extract the user's message
        messages = body.get("messages", [])
        if not messages:
            return json.dumps({"answer": "No messages provided", "source": None})
        
        # Get the last user message
        user_message = None
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break
        
        if not user_message:
            return json.dumps({"answer": "No user message found", "source": None})
        
        # Get search system
        system = get_search_system()
        
        # Perform search
        result = system.search(user_message)
        
        # Emit status if event emitter is available
        if __event_emitter__:
            if result["source"]:
                status_msg = f"Found answer (score: {result['source']['score']}) from {result['source']['doc_id']}"
            else:
                status_msg = "No reliable answer found in documents"
                
            await __event_emitter__({
                "type": "status",
                "data": {
                    "description": status_msg,
                    "done": True
                }
            })
        
        # Return JSON response
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        error_msg = f"Document Embedding Search Error: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        
        if __event_emitter__:
            await __event_emitter__({
                "type": "status", 
                "data": {
                    "description": error_msg,
                    "done": True
                }
            })
        
        return json.dumps({
            "answer": f"Error: {str(e)}",
            "source": None
        })
