# Final Integration Guide: Document Embedding Search with Open WebUI

## ✅ System Ready for Integration

Your document embedding search system is **fully tested and ready** for Open WebUI integration. Here's how to get it working with your Docker setup.

## 🚀 Quick Integration Steps

### Step 1: Stop Current Container
```bash
docker stop open-webui
docker rm open-webui
```

### Step 2: Run with Function Integration
```bash
docker run -d -p 3000:8080 \
  --add-host=host.docker.internal:host-gateway \
  -v open-webui:/app/backend/data \
  -v $(pwd)/documents:/app/documents \
  -v $(pwd)/openwebui_embedding_search.py:/app/functions/openwebui_embedding_search.py \
  --name open-webui ghcr.io/open-webui/open-webui:main
```

### Step 3: Access Open WebUI
Open your browser and go to: `http://localhost:3000`

## 🎯 What You'll Get

### Exact Behavior as Specified

When you ask questions in Open WebUI, you'll get **JSON responses** that follow your exact specifications:

#### ✅ High Confidence Answers (Score ≥ 0.70)
```json
{
  "answer": "Sentry is a monitoring platform that helps developers track errors and performance issues.",
  "source": {
    "doc_id": "doc",
    "chunk_id": 0,
    "score": 0.8021,
    "excerpt": "Sentry Monitoring ve Veri Altyapısı..."
  }
}
```

#### ✅ Low Confidence Answers (Score < 0.70)
```json
{
  "answer": "Insufficient source — no reliable answer from available documents.",
  "source": null
}
```

### System Behavior

1. **Search document embedding index** ✅
2. **Compute cosine similarity scores for each chunk** ✅
3. **Sort results by score in descending order** ✅
4. **Select only the single chunk with the highest score** ✅
5. **If top score < 0.70: respond with "Insufficient source" message** ✅
6. **Base answer only on selected chunk; no hallucination** ✅
7. **Keep answer short and clear; show source info** ✅
8. **Output format must be JSON** ✅

## 🧪 Test Queries

Try these in Open WebUI to verify the system:

### High Confidence Queries
- "What is Sentry?" → Should return detailed answer with high score
- "How does ClickHouse work?" → Should return relevant information
- "Explain monitoring systems" → Should find relevant content

### Threshold Test Queries
- "How to cook pasta?" → May trigger threshold depending on content
- "What is quantum physics?" → Likely to trigger "Insufficient source"
- "Latest stock market trends" → Should trigger "Insufficient source"

## ⚙️ Configuration Options

You can customize the system with environment variables:

```bash
docker run -d -p 3000:8080 \
  --add-host=host.docker.internal:host-gateway \
  -v open-webui:/app/backend/data \
  -v $(pwd)/documents:/app/documents \
  -v $(pwd)/openwebui_embedding_search.py:/app/functions/openwebui_embedding_search.py \
  -e EMBEDDING_THRESHOLD="0.75" \
  -e EMBEDDING_MODEL="intfloat/multilingual-e5-base" \
  -e EMBEDDING_CHUNK_SIZE="400" \
  --name open-webui ghcr.io/open-webui/open-webui:main
```

### Available Settings

| Variable | Default | Description |
|----------|---------|-------------|
| `EMBEDDING_THRESHOLD` | `0.70` | Minimum similarity threshold |
| `EMBEDDING_MODEL` | `intfloat/multilingual-e5-base` | Embedding model |
| `EMBEDDING_CHUNK_SIZE` | `300` | Chunk size in words |
| `EMBEDDING_OVERLAP` | `50` | Overlap between chunks |
| `EMBEDDING_DOCUMENTS_PATH` | `/app/documents` | Documents folder path |

## 🔧 Alternative Setup (If Mounting Fails)

If volume mounting doesn't work, copy files directly:

```bash
# Start container normally
docker run -d -p 3000:8080 \
  --add-host=host.docker.internal:host-gateway \
  -v open-webui:/app/backend/data \
  --name open-webui ghcr.io/open-webui/open-webui:main

# Copy files
docker cp ./documents open-webui:/app/documents
docker cp ./openwebui_embedding_search.py open-webui:/app/functions/

# Restart to load function
docker restart open-webui
```

## 🔍 Verification Steps

### 1. Check Function Loading
```bash
docker logs open-webui | grep "Document Embedding Search"
```

### 2. Verify Documents
```bash
docker exec open-webui ls -la /app/documents/
```

### 3. Test Function Directly
```bash
docker exec open-webui python3 -c "
import sys
sys.path.append('/app/functions')
from openwebui_embedding_search import get_search_system
system = get_search_system()
result = system.search('What is Sentry?')
print(result)
"
```

## 📊 Expected Performance

- **First Query**: 2-5 seconds (model loading + embedding creation)
- **Subsequent Queries**: < 1 second (embeddings cached)
- **Memory Usage**: ~500MB for model + document embeddings
- **Accuracy**: High-quality multilingual embeddings

## 🎯 Success Indicators

When working correctly, you should see:

✅ **Function loads in Open WebUI admin panel**
✅ **Documents are processed and indexed**
✅ **Queries return JSON responses in chat**
✅ **High-confidence queries show answers with source metadata**
✅ **Low-confidence queries show "Insufficient source" message**
✅ **Threshold behavior works as expected**

## 🚨 Troubleshooting

### Function Not Found
- Check if file is mounted: `docker exec open-webui ls /app/functions/`
- Restart container: `docker restart open-webui`

### Documents Not Loading
- Verify mount: `docker exec open-webui ls /app/documents/`
- Check file permissions
- Ensure PDF/TXT files are present

### Dependencies Missing
```bash
docker exec open-webui pip install sentence-transformers numpy pypdf
docker restart open-webui
```

### Memory Issues
- Reduce chunk size: `-e EMBEDDING_CHUNK_SIZE="200"`
- Use smaller model: `-e EMBEDDING_MODEL="intfloat/multilingual-e5-small"`

## 🎉 You're Ready!

The system is **production-ready** and implements your exact specifications:

- **Single chunk selection** with highest similarity score
- **Threshold-based filtering** (0.70 default)
- **JSON output format** with answer and source metadata
- **No hallucination** - answers strictly from source documents
- **Cosine similarity** computation for all chunks
- **"Insufficient source" message** for low-confidence queries

Your Open WebUI will now provide precise, source-backed answers with complete traceability!
