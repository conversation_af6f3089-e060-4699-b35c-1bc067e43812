#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Demo script for Document Embedding Search System

This script demonstrates the exact behavior specified:
1. Search document embedding index
2. Compute cosine similarity scores for each chunk
3. Sort results by score in descending order
4. Select only the single chunk with the highest score
5. If top score < 0.70: respond with "Insufficient source" message
6. Base answer only on selected chunk; no hallucination
7. Keep answer short and clear; show source info
8. Output format must be JSON
"""

import json
from document_embedding_search import DocumentEmbeddingSearch, load_documents

def demo_search_system():
    """Demonstrate the document embedding search system"""
    
    print("Document Embedding Search System Demo")
    print("=" * 50)
    
    # Load documents
    print("Loading documents from ./documents folder...")
    documents = load_documents("./documents")
    print(f"Loaded {len(documents)} documents\n")
    
    # Initialize search system
    search_system = DocumentEmbeddingSearch(threshold=0.70)
    search_system.index_documents(documents)
    
    # Demo queries with expected behavior
    demo_queries = [
        {
            "query": "What is Sentry?",
            "expected": "High similarity - should return answer with source"
        },
        {
            "query": "How does ClickHouse work?", 
            "expected": "Medium similarity - should return answer with source"
        },
        {
            "query": "What is quantum physics?",
            "expected": "Low similarity - may trigger threshold"
        }
    ]
    
    print("Testing queries:")
    print("-" * 30)
    
    for i, demo in enumerate(demo_queries, 1):
        query = demo["query"]
        expected = demo["expected"]
        
        print(f"\nQuery {i}: {query}")
        print(f"Expected: {expected}")
        print("Result:")
        
        result = search_system.search(query)
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        # Show key metrics
        if result["source"]:
            score = result["source"]["score"]
            doc_id = result["source"]["doc_id"]
            chunk_id = result["source"]["chunk_id"]
            print(f"✓ Score: {score:.4f} | Doc: {doc_id} | Chunk: {chunk_id}")
        else:
            print("✗ Below threshold - insufficient source")
        
        print("-" * 50)

def demo_threshold_behavior():
    """Demonstrate threshold behavior with different values"""
    
    print("\nThreshold Behavior Demo")
    print("=" * 30)
    
    documents = load_documents("./documents")
    query = "What is machine learning?"  # Somewhat unrelated query
    
    thresholds = [0.5, 0.7, 0.8, 0.9]
    
    for threshold in thresholds:
        print(f"\nTesting with threshold: {threshold}")
        search_system = DocumentEmbeddingSearch(threshold=threshold)
        search_system.index_documents(documents)
        
        result = search_system.search(query)
        
        if result["source"]:
            score = result["source"]["score"]
            print(f"✓ Score: {score:.4f} >= {threshold} - Answer provided")
        else:
            print(f"✗ Score < {threshold} - Insufficient source")

def demo_json_format():
    """Demonstrate the exact JSON format requirements"""
    
    print("\nJSON Format Demo")
    print("=" * 20)
    
    documents = load_documents("./documents")
    search_system = DocumentEmbeddingSearch(threshold=0.70)
    search_system.index_documents(documents)
    
    # High confidence query
    print("\n1. High confidence query (score >= 0.70):")
    result1 = search_system.search("What is Sentry?")
    print("JSON Output:")
    print(json.dumps(result1, ensure_ascii=False, indent=2))
    
    # Low confidence query  
    print("\n2. Low confidence query (score < 0.70):")
    search_system_strict = DocumentEmbeddingSearch(threshold=0.95)  # Very high threshold
    search_system_strict.index_documents(documents)
    result2 = search_system_strict.search("What is quantum computing?")
    print("JSON Output:")
    print(json.dumps(result2, ensure_ascii=False, indent=2))

def main():
    """Main demo function"""
    try:
        # Main search demo
        demo_search_system()
        
        # Threshold behavior demo
        demo_threshold_behavior()
        
        # JSON format demo
        demo_json_format()
        
        print("\n" + "=" * 60)
        print("Demo completed successfully!")
        print("\nKey Features Demonstrated:")
        print("✓ Single highest-scoring chunk selection")
        print("✓ Cosine similarity computation")
        print("✓ Threshold-based filtering (0.70 default)")
        print("✓ No hallucination - answers from source only")
        print("✓ Structured JSON output format")
        print("✓ Source information with doc_id, chunk_id, score, excerpt")
        
    except Exception as e:
        print(f"Demo error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
