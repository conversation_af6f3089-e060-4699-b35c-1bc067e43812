# Open WebUI Integration Setup

This guide shows how to integrate the Document Embedding Search system with Open WebUI using your Docker setup.

## Current Docker Command Analysis

Your current command:
```bash
docker run -d -p 3000:8080 \
  --add-host=host.docker.internal:host-gateway \
  -v open-webui:/app/backend/data \
  --name open-webui ghcr.io/open-webui/open-webui:main
```

## Integration Steps

### 1. Prepare the Function File

The `openwebui_embedding_search.py` file is ready for Open WebUI integration. It implements the exact specifications:

- ✅ Single chunk selection with highest score
- ✅ Cosine similarity computation  
- ✅ Threshold-based filtering (0.70 default)
- ✅ JSON output format
- ✅ "Insufficient source" message for low scores
- ✅ No hallucination - answers from source only

### 2. Mount Documents and Function

Update your Docker command to include the documents and function:

```bash
# Stop existing container
docker stop open-webui
docker rm open-webui

# Run with mounted documents and function
docker run -d -p 3000:8080 \
  --add-host=host.docker.internal:host-gateway \
  -v open-webui:/app/backend/data \
  -v $(pwd)/documents:/app/documents \
  -v $(pwd)/openwebui_embedding_search.py:/app/functions/openwebui_embedding_search.py \
  --name open-webui ghcr.io/open-webui/open-webui:main
```

### 3. Alternative: Copy Files into Container

If mounting doesn't work, copy files directly:

```bash
# Start your container normally
docker run -d -p 3000:8080 \
  --add-host=host.docker.internal:host-gateway \
  -v open-webui:/app/backend/data \
  --name open-webui ghcr.io/open-webui/open-webui:main

# Copy documents and function into container
docker cp ./documents open-webui:/app/documents
docker cp ./openwebui_embedding_search.py open-webui:/app/functions/

# Restart container to load the function
docker restart open-webui
```

### 4. Environment Variables (Optional)

You can customize the behavior with environment variables:

```bash
docker run -d -p 3000:8080 \
  --add-host=host.docker.internal:host-gateway \
  -v open-webui:/app/backend/data \
  -v $(pwd)/documents:/app/documents \
  -v $(pwd)/openwebui_embedding_search.py:/app/functions/openwebui_embedding_search.py \
  -e EMBEDDING_DOCUMENTS_PATH="/app/documents" \
  -e EMBEDDING_MODEL="intfloat/multilingual-e5-base" \
  -e EMBEDDING_THRESHOLD="0.70" \
  -e EMBEDDING_CHUNK_SIZE="300" \
  -e EMBEDDING_OVERLAP="50" \
  --name open-webui ghcr.io/open-webui/open-webui:main
```

## Using the Function in Open WebUI

### 1. Access Open WebUI

Open your browser and go to: `http://localhost:3000`

### 2. Enable the Function

1. Go to **Admin Panel** → **Functions**
2. Find "Document Embedding Search" function
3. Enable it
4. Configure if needed

### 3. Test the System

Ask questions like:
- "What is Sentry?"
- "How does ClickHouse work?"
- "Explain payment integration"

### Expected Behavior

#### High Confidence Query (Score ≥ 0.70)
```json
{
  "answer": "Sentry is a monitoring platform that helps developers track errors and performance issues.",
  "source": {
    "doc_id": "doc",
    "chunk_id": 0,
    "score": 0.8021,
    "excerpt": "Sentry Monitoring ve Veri Altyapısı..."
  }
}
```

#### Low Confidence Query (Score < 0.70)
```json
{
  "answer": "Insufficient source — no reliable answer from available documents.",
  "source": null
}
```

## Troubleshooting

### Function Not Loading

1. Check if the function file is in the right location:
```bash
docker exec open-webui ls -la /app/functions/
```

2. Check container logs:
```bash
docker logs open-webui
```

3. Restart the container:
```bash
docker restart open-webui
```

### Documents Not Found

1. Verify documents are mounted:
```bash
docker exec open-webui ls -la /app/documents/
```

2. Check the documents path in environment variables

### Dependencies Missing

The function requires these packages (usually pre-installed in Open WebUI):
- `sentence-transformers`
- `numpy`
- `pypdf`

If missing, you can install them:
```bash
docker exec open-webui pip install sentence-transformers numpy pypdf
```

## Function Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `EMBEDDING_DOCUMENTS_PATH` | `./documents` | Path to documents folder |
| `EMBEDDING_MODEL` | `intfloat/multilingual-e5-base` | Embedding model name |
| `EMBEDDING_THRESHOLD` | `0.70` | Minimum similarity threshold |
| `EMBEDDING_CHUNK_SIZE` | `300` | Chunk size in words |
| `EMBEDDING_OVERLAP` | `50` | Overlap between chunks |

### Supported File Types

- `.txt` - Plain text files
- `.pdf` - PDF documents

### Model Options

- **Default**: `intfloat/multilingual-e5-base` - Good balance
- **High Quality**: `intfloat/multilingual-e5-large` - Better accuracy
- **Lightweight**: `intfloat/multilingual-e5-small` - Faster

## Testing the Integration

### 1. Direct Function Test

Test the function directly in the container:
```bash
docker exec -it open-webui python3 -c "
import sys
sys.path.append('/app/functions')
from openwebui_embedding_search import get_search_system
system = get_search_system()
result = system.search('What is Sentry?')
print(result)
"
```

### 2. Web Interface Test

1. Open Open WebUI at `http://localhost:3000`
2. Start a new chat
3. Ask: "What is Sentry?"
4. You should see the JSON response with answer and source

### 3. Threshold Test

Ask an unrelated question like "What is quantum physics?" to test the threshold behavior.

## Performance Notes

- **First Query**: May take longer due to model loading and embedding creation
- **Subsequent Queries**: Fast (embeddings are cached)
- **Memory Usage**: Scales with document size and number of chunks
- **Model Download**: First run downloads the embedding model (~500MB)

## Integration Success Indicators

✅ **Function loads without errors**
✅ **Documents are found and processed**
✅ **Embeddings are created successfully**
✅ **Queries return JSON responses**
✅ **High-confidence queries return answers with source**
✅ **Low-confidence queries return "Insufficient source" message**
✅ **Threshold behavior works correctly**

The system will work exactly as demonstrated in the terminal tests, but now integrated with Open WebUI's chat interface!
