# RAG System - Restructured Architecture

A modular, production-ready Retrieval-Augmented Generation (RAG) system with hybrid search capabilities, built with clean architecture principles.

## 🏗️ Architecture Overview

```
apps/
├── api/                    # API Layer
│   └── main.py            # FastAPI server with endpoints
├── ui/                     # User Interface Layer  
│   └── retrieval_demo.py  # Streamlit demo application
├── core/                   # Core Business Logic
│   ├── embed/             # Embedding functionality
│   │   └── embeddings.py  # Sentence transformer models
│   ├── index/             # Document indexing
│   │   └── vector_index.py # FAISS and BM25 indices
│   ├── ingest/            # Document processing
│   │   ├── chunking.py    # Text chunking strategies
│   │   └── io_utils.py    # File I/O utilities
│   ├── rag/               # RAG pipeline
│   │   ├── pipeline.py    # Main RAG orchestration
│   │   └── multi_doc_rag.py # Multi-document implementation
│   └── retriever/         # Retrieval components
│       ├── diversity.py   # MMR and diversity selection
│       ├── reranking.py   # Semantic and cross-encoder reranking
│       ├── retrieval.py   # Hybrid search implementation
│       └── similarity.py  # Similarity calculations
├── data/                   # Data Layer
│   └── raw/               # Raw document storage
├── cache/                  # System cache
├── logs/                   # Application logs
├── config.py              # Configuration management
├── setup.py               # Setup and installation
├── requirements.txt       # Dependencies
└── __main__.py            # Main entry point
```

## ✨ Features

### Advanced RAG Pipeline
- **Hybrid Retrieval**: Combines BM25 (lexical) and dense vector search (semantic)
- **RRF Fusion**: Reciprocal Rank Fusion for optimal result combination
- **Semantic Reranking**: Using sentence transformers for final ranking
- **Document Diversity**: Ensures balanced results across multiple documents
- **Duplicate Detection**: Automatic handling of similar content chunks
- **MMR Selection**: Maximum Marginal Relevance for result diversity

### Multiple Interfaces
- **REST API**: FastAPI-based server for programmatic access
- **Web UI**: Streamlit-based interactive demo
- **CLI**: Command-line interface for batch processing

### Production Ready
- **Modular Architecture**: Clean separation of concerns
- **Configuration Management**: Environment-based configuration
- **Comprehensive Logging**: Structured logging throughout
- **Error Handling**: Robust error handling and recovery
- **Performance Monitoring**: Built-in benchmarking and metrics

## 🚀 Quick Start

### 1. Setup

```bash
# Install and setup the system
cd apps
python setup.py

# Or manually:
pip install -r requirements.txt
python -m apps setup
```

### 2. Add Documents

```bash
# Place your documents in the data directory
cp your_documents.pdf apps/data/raw/
```

### 3. Run the System

#### Option A: API + UI (Recommended)
```bash
# Terminal 1: Start API server
python -m apps api

# Terminal 2: Start UI
python -m apps ui
```

Then open http://localhost:8501 in your browser.

#### Option B: Command Line
```bash
python -m apps cli --docs apps/data/raw --query "What is Sentry?"
```

#### Option C: Direct Python
```python
from apps.core.rag.multi_doc_rag import MultiDocumentRAG

# Initialize RAG system
rag = MultiDocumentRAG(documents_path="apps/data/raw")
rag.initialize()

# Query the system
response = rag.query("What is Sentry and how does it work?")
rag.print_results(response)
```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file in the apps directory:

```bash
# Document Processing
RAG_DOCUMENTS_PATH=./apps/data/raw
RAG_CHUNK_SIZE=300
RAG_OVERLAP=50

# Models
RAG_DENSE_MODEL=intfloat/multilingual-e5-base

# Retrieval Settings
RAG_SIMILARITY_THRESHOLD=0.85
RAG_MAX_PER_DOC=3
RAG_TOPK=50
RAG_FINAL_K=5

# API Settings
API_HOST=0.0.0.0
API_PORT=8000

# UI Settings
UI_PORT=8501
RAG_API_URL=http://localhost:8000
```

### Programmatic Configuration

```python
from apps.config import update_config

# Update configuration
update_config(
    chunk_size=500,
    similarity_threshold=0.9,
    dense_model="sentence-transformers/all-mpnet-base-v2"
)
```

## 📚 API Documentation

### Start API Server
```bash
python -m apps api
```

### API Endpoints

- **GET /**: Health check
- **POST /initialize**: Initialize/reinitialize the system
- **POST /query**: Query documents
- **GET /stats**: Get system statistics
- **GET /documents**: List available documents
- **POST /benchmark**: Benchmark query performance

### Interactive API Docs
Visit http://localhost:8000/docs for Swagger UI documentation.

### Example API Usage

```python
import requests

# Initialize system
response = requests.post("http://localhost:8000/initialize", json={
    "documents_path": "./apps/data/raw",
    "embedding_model": "intfloat/multilingual-e5-base"
})

# Query documents
response = requests.post("http://localhost:8000/query", json={
    "query": "What is Sentry?",
    "topk": 50,
    "final_k": 5
})

results = response.json()
```

## 🎨 UI Features

The Streamlit UI provides:

- **Interactive Configuration**: Adjust system parameters in real-time
- **Query Interface**: Natural language querying with parameter controls
- **Results Visualization**: Formatted display of retrieved chunks with metadata
- **System Monitoring**: Real-time statistics and health status
- **Document Browser**: Overview of loaded documents and chunks
- **Performance Benchmarking**: Built-in query performance testing

## 🔧 Advanced Usage

### Custom Embedding Models

```python
from apps.core.rag.multi_doc_rag import MultiDocumentRAG

rag = MultiDocumentRAG(
    documents_path="./apps/data/raw",
    embedding_model="sentence-transformers/all-mpnet-base-v2",
    rerank_method="hybrid",
    cross_model="cross-encoder/ms-marco-MiniLM-L-6-v2"
)
```

### Batch Processing

```python
queries = [
    "What is Sentry?",
    "How does ClickHouse work?",
    "What are the performance benefits?"
]

for query in queries:
    response = rag.query(query)
    rag.export_results(response, f"results_{query[:20]}.json")
```

### Performance Tuning

```python
# For large document sets
rag = MultiDocumentRAG(
    documents_path="./large_docs",
    chunk_size=500,           # Larger chunks
    topk=100,                # More initial candidates
    similarity_threshold=0.9  # Stricter duplicate detection
)
```

## 🧪 Testing

```bash
# Run all tests (when implemented)
python -m pytest apps/tests/

# Test specific module
python -m pytest apps/tests/test_retrieval.py

# Test with coverage
python -m pytest --cov=apps apps/tests/
```

## 📊 Performance

### Benchmarking

```bash
# CLI benchmark
python -m apps cli --docs apps/data/raw --query "test query" --benchmark 10

# API benchmark
curl -X POST "http://localhost:8000/benchmark" \
  -H "Content-Type: application/json" \
  -d '{"query": "test query"}' \
  -G -d "iterations=10"
```

### Optimization Tips

1. **Use appropriate chunk sizes**: 300-500 words for most documents
2. **Adjust similarity threshold**: Higher values (0.9+) for stricter duplicate detection
3. **Tune MMR lambda**: 0.7 balances relevance and diversity
4. **Enable caching**: Set `RAG_ENABLE_CACHING=true`
5. **Use GPU**: Install `faiss-gpu` for faster vector search

## 🔍 Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed
   ```bash
   pip install -r apps/requirements.txt
   ```

2. **No Documents Found**: Check document path and file formats
   ```bash
   ls -la apps/data/raw/
   ```

3. **API Connection Issues**: Verify API server is running
   ```bash
   curl http://localhost:8000/health
   ```

4. **Memory Issues**: Reduce batch size or chunk size
   ```python
   update_config(batch_size=16, chunk_size=200)
   ```

### Logging

Check logs for detailed error information:
```bash
tail -f apps/logs/rag_system.log
```

## 🤝 Contributing

1. Follow the modular architecture
2. Add tests for new functionality
3. Update documentation
4. Use type hints and docstrings
5. Follow PEP 8 style guidelines

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- Built with sentence-transformers, FAISS, FastAPI, and Streamlit
- Inspired by modern RAG research and best practices
- Designed for production deployment and scalability
