# Document Embedding Search System

A precise document embedding search system that follows strict retrieval and response guidelines.

## Overview

This system implements a document embedding search with the following exact specifications:

1. **Search Process**: Computes cosine similarity scores for all document chunks
2. **Single Result**: Returns only the highest-scoring chunk
3. **Threshold Check**: If top score < 0.70, returns "Insufficient source" message
4. **No Hallucination**: Answers are based strictly on the selected chunk
5. **JSON Output**: All responses are in structured JSON format

## Features

- **Document Loading**: Supports PDF and TXT files
- **Smart Chunking**: Sentence-aware chunking that respects boundaries
- **Embedding Models**: Uses multilingual E5 embeddings by default
- **LLM Integration**: Optional Qwen2-1.5B-Instruct for answer generation
- **Threshold Control**: Configurable similarity threshold
- **JSON Output**: Structured response format

## Installation

Ensure you have the required dependencies:

```bash
pip install -r requirements.txt
```

For LLM support, you may need additional packages:

```bash
pip install torch transformers accelerate
```

## Usage

### Basic Usage

```bash
python document_embedding_search.py --docs-folder ./documents --query "What is Sentry?"
```

### With LLM Answer Generation

```bash
python document_embedding_search.py \
    --docs-folder ./documents \
    --query "How does ClickHouse work?" \
    --llm-model "Qwen/Qwen2-1.5B-Instruct"
```

### Custom Parameters

```bash
python document_embedding_search.py \
    --docs-folder ./documents \
    --query "Explain payment integration" \
    --model "intfloat/multilingual-e5-base" \
    --threshold 0.75 \
    --chunk-size 400 \
    --overlap 60
```

## Command Line Arguments

- `--docs-folder`: Path to documents folder or single file (required)
- `--query`: Search query (required)
- `--model`: Embedding model name (default: "intfloat/multilingual-e5-base")
- `--llm-model`: LLM model for answer generation (optional)
- `--threshold`: Minimum similarity threshold (default: 0.70)
- `--chunk-size`: Chunk size in words (default: 300)
- `--overlap`: Overlap between chunks in words (default: 50)

## Output Format

The system always outputs JSON in this exact format:

### Successful Response
```json
{
  "answer": "Sentry is a monitoring platform that helps developers track errors and performance issues.",
  "source": {
    "doc_id": "sentry_guide",
    "chunk_id": 42,
    "score": 0.8734,
    "excerpt": "Sentry is a monitoring platform that helps developers track errors and performance issues. It provides real-time error tracking..."
  }
}
```

### Insufficient Source Response
```json
{
  "answer": "Insufficient source — no reliable answer from available documents.",
  "source": null
}
```

## System Behavior

### Search Algorithm

1. **Document Processing**: Load and chunk documents with sentence-aware splitting
2. **Embedding Creation**: Generate embeddings using E5 format ("passage: ..." prefix)
3. **Query Processing**: Encode query with E5 format ("query: ..." prefix)
4. **Similarity Computation**: Calculate cosine similarity between query and all chunks
5. **Ranking**: Sort results by similarity score in descending order
6. **Selection**: Take only the single highest-scoring chunk
7. **Threshold Check**: Verify score meets minimum threshold (default 0.70)
8. **Answer Generation**: Extract or generate answer from selected chunk

### Answer Generation Modes

1. **Simple Extraction** (default): Returns first 1-2 sentences from the chunk
2. **LLM Generation** (with --llm-model): Uses Qwen2-1.5B-Instruct to generate focused answers

### Threshold Behavior

- **Score ≥ 0.70**: Returns answer with source information
- **Score < 0.70**: Returns "Insufficient source" message with null source

## Testing

Run the test suite to verify functionality:

```bash
python test_embedding_search.py
```

This will test:
- Document loading and indexing
- Query processing with various examples
- Threshold behavior
- LLM integration (if available)

## Example Queries

### High-Confidence Queries (likely > 0.70 threshold)
- "What is Sentry?"
- "How does ClickHouse work?"
- "Explain payment integration"

### Low-Confidence Queries (likely < 0.70 threshold)
- "What is quantum physics?"
- "How to cook pasta?"
- "Latest stock market trends"

## Model Recommendations

### Embedding Models
- **Default**: `intfloat/multilingual-e5-base` - Good balance of quality and speed
- **High Quality**: `intfloat/multilingual-e5-large` - Better accuracy, slower
- **Lightweight**: `intfloat/multilingual-e5-small` - Faster, lower accuracy

### LLM Models
- **Recommended**: `Qwen/Qwen2-1.5B-Instruct` - Efficient and capable
- **Alternative**: `microsoft/DialoGPT-medium` - Conversational responses
- **Lightweight**: `distilgpt2` - Very fast, basic responses

## Performance Considerations

- **Memory Usage**: Scales with number of documents and chunk size
- **Processing Time**: Initial indexing takes time; queries are fast
- **GPU Support**: LLM models benefit from GPU acceleration
- **Batch Processing**: Consider batching for multiple queries

## Troubleshooting

### Common Issues

1. **No documents found**: Ensure documents are in the specified folder
2. **Low similarity scores**: Try adjusting chunk size or overlap
3. **LLM errors**: Check model availability and system resources
4. **Memory issues**: Reduce chunk size or use smaller models

### Debug Tips

- Check document loading with verbose output
- Test with simple, direct queries first
- Verify embedding model compatibility
- Monitor similarity scores in output

## Integration

The system can be easily integrated into larger applications:

```python
from document_embedding_search import DocumentEmbeddingSearch, load_documents

# Initialize
documents = load_documents("./documents")
search_system = DocumentEmbeddingSearch(threshold=0.70)
search_system.index_documents(documents)

# Search
result = search_system.search("Your query here")
print(result["answer"])
```

## License

This implementation follows the exact specifications provided and is designed for precise, reliable document retrieval with strict quality controls.
